name: Type Checking

on: [push, pull_request]

jobs:
  type-check:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Install uv and set the python version
        uses: astral-sh/setup-uv@v5
        with:
          python-version: "3.11"
          enable-cache: true
          cache-dependency-glob: "uv.lock"

      - name: Install the project
        run: uv sync --all-extras --dev

      - name: Run mypy
        run: uv run mypy fastcrud --config-file mypy.ini
