# Submit Your Project

!!! tip "Share Your Work"
    Have you built something with FastCRUD? We'd love to feature it in our showcase!

## Project Categories

!!! example "What You Can Submit"
    - **Tutorials**: Step-by-step guides teaching others how to build with FastCRUD
    - **Open Source Projects**: Libraries, tools, or applications others can use and learn from
    - **Applications**: Web apps, desktop tools, or services built with FastCRUD
    - **Commercial Services**: Products or services powered by FastCRUD

## How to Submit

!!! info "Submission Steps"
    1. Create a new issue using our [Showcase Submission Template](https://github.com/igorbenav/fastcrud/issues/new?assignees=&labels=showcase&projects=&template=showcase-submission.md&title=Showcase%3A+)
    2. Fill in the relevant information for your project type
    3. We'll review your submission and add it to the showcase!

## Requirements by Category

!!! success "What We Look For"
    **For Tutorials:**
    
    - Clear step-by-step instructions
    - Working code examples
    - Explanation of concepts used

    **For Open Source Projects:**
    
    - Public repository
    - Basic documentation
    - Installation/usage instructions

    **For Applications/Services:**
    
    - Public demo or screenshots
    - Description of FastCRUD features used
    - Link to live service (if applicable)

## Need Help?

!!! question "Questions?"
    Need help with your submission? We're here to help!

    - Open a discussion on [GitHub](https://github.com/igorbenav/fastcrud/discussions)