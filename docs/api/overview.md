# API Reference Overview

Welcome to the API Reference section of our documentation. This section provides detailed information about the various classes, functions, and modules that make up our application. Whether you are looking to extend the application, integrate with it, or simply explore its capabilities, this section will guide you through the intricacies of our codebase.

## Key Components

Our application's API is comprised of several key components, each serving a specific purpose:

1. **FastCRUD Class**: This class is at the heart of our CRUD (Create, Read, Update, Delete) operations. It provides a convenient and efficient way to interact with our database models.

    - [FastCRUD Class Reference](fastcrud.md)

2. **EndpointCreator Class**: A utility class for creating and registering CRUD endpoints in a FastAPI application. It simplifies the addition of standard CRUD endpoints to your FastAPI router.

    - [EndpointCreator Class Reference](endpoint_creator.md)

3. **crud_router Function**: A function that creates and configures a FastAPI router with CRUD endpoints for a specific model. It's a quick way to add standard CRUD operations to your models.

    - [CRUD Router Function Reference](crud_router.md)

4. **paginated Module**: A a utility module for offset pagination related functions.

    - [Pagination Module Reference](paginated.md)

## Usage

Each component is documented with its own dedicated page, where you can find detailed information about its methods, parameters, return types, and usage examples. These pages are designed to provide you with all the information you need to understand and work with our API effectively.

## Contribution

If you wish to contribute to the development of our API, please refer to our [Contributing Guidelines](../community/CONTRIBUTING.md). We welcome contributions of all forms, from bug fixes to feature development.

## Feedback

Your feedback is crucial in helping us improve this documentation. If you have any suggestions, corrections, or queries, please reach out to us.

---

Navigate through each section for detailed documentation of our API components.
