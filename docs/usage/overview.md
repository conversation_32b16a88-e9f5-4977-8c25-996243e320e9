# Usage Overview

The Usage section of our documentation provides comprehensive guides on how to effectively use key features of our application. This section is divided into various topics, each focusing on a specific aspect of usage, ensuring that you have all the information you need to leverage the full potential of our tools and functionalities.

## Key Topics

### 1. Automatic Endpoint Creation with `crud_router`

This guide covers the use of `crud_router` for automatic endpoint creation in FastAPI applications. It provides a step-by-step approach to streamline the creation of standard CRUD endpoints.

- [Automatic Endpoint Creation Guide](endpoint.md)

### 2. Enhanced CRUD Operations with FastCRUD

Learn how to use the `FastCRUD` class for enhanced CRUD operations. This guide delves into the functionalities offered by `FastCRUD`, including advanced query capabilities, pagination, and error handling.

- [FastCRUD Usage Guide](crud.md)

## Getting Started

To make the most out of these guides, we recommend familiarizing yourself with FastAPI and SQLAlchemy basics, as our application leverages these frameworks extensively.

## Contribution

If you have suggestions or contributions to these guides, please refer to our [Contributing Guidelines](../community/CONTRIBUTING.md). We appreciate your input in improving our documentation.
