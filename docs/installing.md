## Requirements

Before installing FastCRUD, ensure you have the following prerequisites:

* **Python:** Version 3.9 or newer.
* **FastAPI:** FastCRUD is built to work with FastAPI, so having FastAPI in your project is essential.
* **SQLAlchemy or SQLModel:** FastCRUD uses SQLAlchemy 2.0 for database operations, so you need SQLAlchemy 2.0 or newer or SQLModel 0.14 or newer.
* **Pydantic V2 or SQLModel:** FastCRUD leverages Pydantic models for data validation and serialization, so you need Pydantic 2.0 or newer or SQLModel 0.14 or newer.

## Installing

To install, just run:

```sh
pip install fastcrud
```

Or, if using UV:

```sh
uv add fastcrud
```
