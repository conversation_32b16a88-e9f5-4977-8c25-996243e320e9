# Showcase

!!! example "**Categories**"
    Browse by type: [Applications](#applications) · [Tutorials](#tutorials)

## Applications

!!! tip "Be the First!"
    No applications yet. Have you built something with FastCRUD? We'd love to feature it here!

    **[Submit Your Project](community/showcase_submission.md)**

## Open Source

!!! success "FastAPI Boilerplate"

    **By FastCRUD Team** · [Source](https://github.com/igorbenav/FastAPI-boilerplate)

    FastAPI boilerplate creates an extendable async API using FastAPI, Pydantic V2, SQLAlchemy 2.0 and PostgreSQL.

    `Template` `FastAPI` `Boilerplate`

!!! success "Email Assistant API"

    **By FastCRUD Team** · [Source](https://github.com/igorbenav/email-assistant-api)

    A personalized email writing assistant built with FastAPI, OpenAI's API, and FastCRUD.

    `OpenAI` `LLM` `AI`

!!! success "SQLModel Boilerplate"

    **By FastCRUD Team** · [Source](https://github.com/igorbenav/SQLModel-boilerplate)

    SQLModel boilerplate creates an extendable async API using FastAPI, SQLModel and PostgreSQL.

    `Template` `SQLModel` `Boilerplate`

## Tutorials

!!! note "Creating LLM Powered APIs with FastAPI"
    **By FastCRUD Team** · [View Tutorial](https://medium.com/@igorbenav/creating-llm-powered-apis-with-fastapi-in-2024-aecb02e40b8f)

    Learn the basics of creating LLM powered APIs with FastCRUD.

    `FastAPI` `AI` `Beginner` `LLM`

---

!!! question "Add Your Project"
    Built something with FastCRUD? We'd love to showcase it!
    
    **[Submit Your Project](community/showcase_submission.md)**