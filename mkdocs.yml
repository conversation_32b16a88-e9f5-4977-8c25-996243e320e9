site_name: FastCRUD
site_description: Powerful CRUD methods and automatic endpoint creation for FastAPI.
site_author: <PERSON>
site_url: https://github.com/igorbenav/fastcrud

theme:
  name: material
  font:
    text: Ubuntu
  logo: assets/logo.png
  favicon: assets/logo.png
  features:
    - navigation.instant
    - navigation.instant.prefetch
    - navigation.tabs
    - navigation.indexes
    - search.suggest
    - content.code.copy
  palette:
    - media: "(prefers-color-scheme: light)"
      scheme: default
      primary: deep purple
      accent: deep purple
      toggle:
        icon: material/brightness-7
        name: Switch to dark mode

    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      primary: deep purple
      accent: deep purple
      toggle:
        icon: material/brightness-4
        name: Switch to light mode

plugins:
  - search
  - mkdocstrings:
      handlers:
        python:
          rendering:
            show_source: true

nav:
  - FastCRUD: index.md
  - Installing: installing.md
  - Quick-Start: quick-start.md
  - Using SQLModel: sqlmodel.md
  - Usage:
    - Overview: usage/overview.md
    - Automatic Endpoints: usage/endpoint.md
    - CRUD Utilities: usage/crud.md
  - Advanced:
    - Overview: advanced/overview.md
    - Custom Endpoints: advanced/endpoint.md
    - Advanced CRUD Usage: advanced/crud.md
    - Advanced Filtering: advanced/filters.md
    - Dependency-Based Filtering: advanced/dependency_filtering.md
    - Joining Models: advanced/joins.md
    - Response Key Configuration: advanced/response_keys.md
  - API Reference:
    - Overview: api/overview.md
    - FastCRUD: api/fastcrud.md
    - EndpointCreator: api/endpoint_creator.md
    - crud_router: api/crud_router.md
    - paginated: api/paginated.md
  - Showcase: showcase.md
  - Community:
    - Overview: community/overview.md
    - Contributing: community/CONTRIBUTING.md
    - Showcase Submission: community/showcase_submission.md
    - Code of Conduct: community/CODE_OF_CONDUCT.md
    - License: community/LICENSE.md
  - Changelog: changelog.md

markdown_extensions:
  - admonition
  - codehilite
  - toc:
      permalink: true
  - pymdownx.details:
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.superfences

repo_name: igorbenav/fastcrud
repo_url: https://github.com/igorbenav/fastcrud
edit_uri: edit/main/docs/
